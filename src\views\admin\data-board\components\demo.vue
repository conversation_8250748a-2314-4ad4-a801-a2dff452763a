<script setup>
import { EchartsUI, useEcharts } from "@/components/Echart"
import { ref, watch } from "vue"

const props = defineProps({
  chartData: {
    type: Object,
    default: () => { }
  },
  height: {
    type: String,
    default: "188px"
  }
})
const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

watch(
  () => props.chartData,
  () => {
    paintChart()
  }
)

function paintChart() {
  renderEcharts({

    backgroundColor: "#fff",
    color: ["#23D486"],
    grid: {
      top: "14%",
      left: "4%",
      right: "2%",
      bottom: "1%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        axisLine: {
          onZero: true,
          lineStyle: {
            color: "#81b0d0"
          }
        },
        axisLabel: {
          interval: 0,
          align: "center",
          margin: 10,
          color: "rgb(139, 143, 147)",
          rotate: 0
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        boundaryGap: false,
        data: props.chartData.xAxis
      }
    ],

    yAxis: [
      {
        type: "value",
        name: "访问系统次数",
        nameTextStyle: {
          // y轴上方单位的颜色
          color: "#6D6F75"
        },
        // splitNumber: 6,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#E2E3E6"
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#E2E3E6",
            type: "dashed"
          }
        },
        axisLabel: {
          color: "#6D6F75"
        },
        axisTick: {
          show: false
        }
      }
    ],
    series: [{
      type: "line",
      stack: "all",
      symbolSize: 12,
      symbol: "circle",
      itemStyle: {
        borderWidth: 2
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}",
        color: "#6D6F75",
        fontSize: 14
      },
      data: props.chartData.yAxis
    }]
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="props.height" />
</template>

<style scoped></style>
